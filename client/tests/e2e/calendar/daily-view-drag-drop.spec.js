const { test, expect } = require('@playwright/test');

test.describe('Daily View Drag and Drop', () => {
  test.beforeEach(async ({ page }) => {
    // Mock the login API
    await page.route('**/api/users/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          _id: 'mock-user-id',
          name: 'Test User',
          email: '<EMAIL>',
          token: 'mock-token'
        })
      });
    });

    // Mock the events API
    await page.route('**/api/events', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'mock-event-id',
            title: 'Mock Event',
            date: new Date().toISOString(),
            location: 'Mock Location',
            description: 'Mock event for testing',
            owner: 'mock-user-id'
          }
        ])
      });
    });

    // Mock the tasks API with a task that has specific duration
    await page.route('**/api/tasks/calendar**', async route => {
      const today = new Date();
      const startTime = new Date(today.setHours(10, 0, 0, 0));

      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'mock-task-id-1',
            name: 'Test Task for Drag Drop',
            taskType: 'Other',
            details: 'This is a test task for drag and drop',
            status: 'Not Started',
            event: 'mock-event-id',
            assignees: [{
              _id: 'mock-user-id',
              name: 'Test User',
              email: '<EMAIL>'
            }],
            startTime: startTime.toISOString(),
            duration: '02:30:00', // 2.5 hours
            softDeadline: null,
            hardDeadline: null
          }
        ])
      });
    });

    // Mock task update API
    await page.route('**/api/tasks/**', async route => {
      if (route.request().method() === 'PUT') {
        const requestBody = await route.request().postDataJSON();
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            ...requestBody,
            _id: 'mock-task-id-1'
          })
        });
      } else {
        await route.continue();
      }
    });

    // Mock users API
    await page.route('**/api/users', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            _id: 'mock-user-id',
            name: 'Test User',
            email: '<EMAIL>'
          }
        ])
      });
    });
  });

  test('should preserve task duration when moving task', async ({ page }) => {
    // Login first
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/events');

    // Navigate to calendar daily view
    await page.goto(`/calendar/daily/${new Date().toISOString().split('T')[0]}`);

    // Wait for the page to load
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(3000); // Wait for tasks to load

    // Find the task element
    const taskElement = page.locator(`[data-task-id="mock-task-id-1"]`);
    await expect(taskElement).toBeVisible();

    // Get initial task position and duration info
    const initialBounds = await taskElement.boundingBox();

    // Verify initial duration is displayed correctly (should show 2h 30m or 02:30:00)
    const taskTooltip = page.locator('div[role="tooltip"]');
    await taskElement.hover();
    await expect(taskTooltip).toBeVisible();
    const tooltipText = await taskTooltip.textContent();
    expect(tooltipText).toContain('02:30:00'); // Should show original duration

    // Move the mouse away to hide tooltip
    await page.mouse.move(0, 0);
    await page.waitForTimeout(500);

    // Drag the task down by 2 hours (120 pixels at default zoom)
    const dragDistance = 120; // 2 hours worth of pixels
    await taskElement.hover();
    await page.mouse.down();
    await page.mouse.move(initialBounds.x + initialBounds.width / 2, initialBounds.y + dragDistance);
    await page.mouse.up();

    // Wait for the drag operation to complete
    await page.waitForTimeout(1000);

    // Verify the task moved but duration is preserved
    const movedTaskElement = page.locator(`[data-task-id="mock-task-id-1"]`);
    await expect(movedTaskElement).toBeVisible();

    // Check that duration is still the same by hovering over the moved task
    await movedTaskElement.hover();
    await expect(taskTooltip).toBeVisible();
    const newTooltipText = await taskTooltip.textContent();

    // The duration should still be 02:30:00, not 01:00:00
    expect(newTooltipText).toContain('02:30:00');
    expect(newTooltipText).not.toContain('01:00:00');

    // Verify the start time changed (should be around 12:00 PM now)
    expect(newTooltipText).toContain('12:00');

    console.log('Task tooltip after drag:', newTooltipText);
  });

  test('should update duration when resizing task', async ({ page }) => {
    // Login first
    await page.goto('/login');
    await page.fill('input[type="email"]', '<EMAIL>');
    await page.fill('input[type="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForURL('**/events');

    // Navigate to calendar daily view
    await page.goto(`/calendar/daily/${new Date().toISOString().split('T')[0]}`);

    // Wait for the page to load
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(3000);

    // Find the task element
    const taskElement = page.locator(`[data-task-id="mock-task-id-1"]`);
    await expect(taskElement).toBeVisible();

    const taskBounds = await taskElement.boundingBox();

    // Resize the task by dragging the bottom edge
    // Move to the bottom edge of the task (within 10px from bottom)
    const bottomEdgeY = taskBounds.y + taskBounds.height - 5;
    await page.mouse.move(taskBounds.x + taskBounds.width / 2, bottomEdgeY);
    await page.mouse.down();

    // Drag down by 1 hour (60 pixels)
    await page.mouse.move(taskBounds.x + taskBounds.width / 2, bottomEdgeY + 60);
    await page.mouse.up();

    // Wait for the resize operation to complete
    await page.waitForTimeout(1000);

    // Verify the duration changed
    const resizedTaskElement = page.locator(`[data-task-id="mock-task-id-1"]`);
    await resizedTaskElement.hover();

    const taskTooltip = page.locator('div[role="tooltip"]');
    await expect(taskTooltip).toBeVisible();
    const tooltipText = await taskTooltip.textContent();

    // Duration should now be 3.5 hours (03:30:00)
    expect(tooltipText).toContain('03:30:00');

    console.log('Task tooltip after resize:', tooltipText);
  });
});

const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://localhost:5000',
      changeOrigin: true,
      timeout: 30000, // 30 second timeout
      proxyTimeout: 30000,
      // Handle connection errors gracefully
      onError: (err, req, res) => {
        console.log('Proxy error:', err.message);
        if (!res.headersSent) {
          res.writeHead(500, {
            'content-type': 'application/json',
          });
          res.end(JSON.stringify({
            error: 'Proxy connection failed',
            message: err.message
          }));
        }
      },
      // Add retry logic for failed connections
      retry: true,
      retryDelay: 1000,
      // Handle socket errors
      onProxyReq: (proxyReq, req, res) => {
        proxyReq.on('error', (err) => {
          console.log('Proxy request error:', err.message);
        });
      },
      onProxyRes: (proxyRes, req, res) => {
        proxyRes.on('error', (err) => {
          console.log('Proxy response error:', err.message);
        });
      }
    })
  );
};

import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Container,
  Box,
  CssBaseline,
  ThemeProvider,
} from '@mui/material';
import { createTheme } from '@mui/material/styles';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';

// Components
import Navbar from './components/Navbar';
import PublicNavbar from './components/PublicNavbar';
import Footer from './components/Footer';

// Pages
import Login from './pages/Login';
import Register from './pages/Register';
import GoogleAuthSuccess from './pages/GoogleAuthSuccess';
import Events from './pages/Events';
import EventDetails from './pages/EventDetails';
import Calendar from './pages/Calendar';
import Templates from './pages/Templates';
import TaskManager from './pages/TaskManager';
import Resources from './pages/Resources';
import VenueManagementWrapper from './components/VenueManagementWrapper';
import GuestManagementWrapper from './components/GuestManagementWrapper';
import StakeholderManagementWrapper from './components/StakeholderManagementWrapper';
import BudgetManagement from './pages/BudgetManagement';
import SuppliesWrapper from './components/SuppliesWrapper';
import UserProfile from './pages/UserProfile';
import NotFound from './pages/NotFound';
import Landing from './pages/Landing';
import Pricing from './pages/Pricing';
import Features from './pages/Features';
import CheckoutPage from './pages/CheckoutPage';
import CompanyInfo from './pages/CompanyInfo';

// Contexts

import { AuthProvider, AuthContext } from './contexts/AuthContext';
import { EventProvider } from './contexts/EventContext';

// Theme

const theme = createTheme({
  palette: {
    primary: {
      main: '#6200ea',
    },
    secondary: {
      main: '#03dac6',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    fontWeightLight: 300,
    fontWeightRegular: 400,
    fontWeightMedium: 500,
    fontWeightBold: 700,
  },
  spacing: 8,
});

// AppContent component that uses hooks
const AppContent = () => {
  const location = useLocation();
  const { i18n } = useTranslation();

  // Initialize language
  useEffect(() => {
    // Normalize language code
    const normalizeLanguage = (langCode) => {
      if (langCode.startsWith('zh')) {
        return 'zh-TW';
      }
      return langCode.split('-')[0] === 'en' ? 'en' : langCode;
    };

    // Get current language or use default
    const currentLang = i18n.language || 'zh-TW';
    const normalizedLang = normalizeLanguage(currentLang);

    // Set language if needed
    if (normalizedLang !== i18n.language) {
      i18n.changeLanguage(normalizedLang);
    }

    console.log('Language initialized to:', normalizedLang);
  }, [i18n]);

  // Function to determine if the current route is a public route
  const isPublicRoute = (pathname) => {
    return pathname === '/' ||
           pathname === '/landing' ||
           pathname === '/pricing' ||
           pathname === '/features' ||
           pathname === '/login' ||
           pathname === '/register' ||
           pathname === '/google-auth-success' ||
           pathname === '/checkout' ||
           pathname === '/payment/success' ||
           pathname === '/about';
  };

  // Protected route component
  const ProtectedRoute = ({ children }) => {
    return (
      <AuthContext.Consumer>
        {({ user, isLoading }) => {
          // If still loading, don't redirect yet
          if (isLoading) {
            return <div>Loading...</div>;
          }
          // If not logged in after loading is complete, redirect to login
          if (!user) {
            return <Navigate to="/login" />;
          }
          return children;
        }}
      </AuthContext.Consumer>
    );
  };

  // Public route component - redirects to events page if user is logged in
  const PublicRoute = ({ children }) => {
    return (
      <AuthContext.Consumer>
        {({ user, isLoading }) => {
          // Only redirect if we're sure the user is logged in (not during loading)
          if (user && !isLoading) {
            return <Navigate to="/" />;
          }
          return children;
        }}
      </AuthContext.Consumer>
    );
  };

  return (
    <AuthProvider>
      <EventProvider>
        <ThemeProvider theme={theme}>
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <CssBaseline />
            <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
              <AuthContext.Consumer>
                {({ user }) => (
                  <>
                    {isPublicRoute(location.pathname) && !user ? <PublicNavbar /> : <Navbar />}
                    <Box component="main" sx={{ flexGrow: 1, p: 0, mt: isPublicRoute(location.pathname) && !user ? 0 : { xs: 6, sm: 7, md: 8 } }}>
                      <Container
                        maxWidth={
                          isPublicRoute(location.pathname) && !user ? false :
                          (location.pathname.includes('/calendar') ? false : "lg")
                        }
                        sx={{
                          p: isPublicRoute(location.pathname) && !user ? 0 :
                             (location.pathname.includes('/calendar') ? { xs: 0.5, sm: 1, md: 1.5 } : { xs: 0.5, sm: 1.5, md: 2 })
                        }}
                      >
                        <Routes>
                          {/* Public routes - accessible without login */}
                          <Route path="/login" element={<Login />} />
                          <Route path="/register" element={<Register />} />
                          <Route path="/auth/google/success" element={<GoogleAuthSuccess />} />
                          <Route path="/pricing" element={<Pricing />} />
                          <Route path="/features" element={<Features />} />
                          <Route path="/checkout" element={<CheckoutPage />} />
                          <Route path="/payment/success" element={<CheckoutPage />} />
                          <Route path="/about" element={<CompanyInfo />} />

                          {/* Main app routes - require authentication */}
                          <Route path="/" element={
                            <AuthContext.Consumer>
                              {({ user }) => user ? <Events /> : <Landing />}
                            </AuthContext.Consumer>
                          } />
                          <Route path="/landing" element={<Landing />} />
                          <Route path="/events" element={<ProtectedRoute><Events /></ProtectedRoute>} />
                          <Route path="/events/:id" element={<ProtectedRoute><EventDetails /></ProtectedRoute>} />
                          <Route path="/tasks/:id" element={<ProtectedRoute><TaskManager /></ProtectedRoute>} />
                          <Route path="/tasks/:id/:view" element={<ProtectedRoute><TaskManager /></ProtectedRoute>} />
                          <Route path="/calendar" element={<ProtectedRoute><Calendar /></ProtectedRoute>} />
                          <Route path="/calendar/:view" element={<ProtectedRoute><Calendar /></ProtectedRoute>} />
                          <Route path="/calendar/:view/:date" element={<ProtectedRoute><Calendar /></ProtectedRoute>} />
                          <Route path="/templates" element={<ProtectedRoute><Templates /></ProtectedRoute>} />
                          <Route path="/resources" element={<ProtectedRoute><Resources /></ProtectedRoute>} />
                          <Route path="/resources/guests" element={<ProtectedRoute><GuestManagementWrapper /></ProtectedRoute>} />
                          <Route path="/resources/guests/:eventId" element={<ProtectedRoute><GuestManagementWrapper /></ProtectedRoute>} />
                          <Route path="/resources/venues" element={<ProtectedRoute><VenueManagementWrapper /></ProtectedRoute>} />
                          <Route path="/resources/venues/:eventId" element={<ProtectedRoute><VenueManagementWrapper /></ProtectedRoute>} />
                          <Route path="/resources/stakeholders" element={<ProtectedRoute><StakeholderManagementWrapper /></ProtectedRoute>} />
                          <Route path="/resources/stakeholders/:eventId" element={<ProtectedRoute><StakeholderManagementWrapper /></ProtectedRoute>} />
                          <Route path="/budget" element={<ProtectedRoute><BudgetManagement /></ProtectedRoute>} />
                          <Route path="/budget/:eventId" element={<ProtectedRoute><BudgetManagement /></ProtectedRoute>} />
                          <Route path="/supplies" element={<ProtectedRoute><SuppliesWrapper /></ProtectedRoute>} />
                          <Route path="/supplies/:eventId" element={<ProtectedRoute><SuppliesWrapper /></ProtectedRoute>} />
                          <Route path="/settings" element={<Navigate to="/profile" />} />
                          <Route path="/profile" element={<ProtectedRoute><UserProfile /></ProtectedRoute>} />
                          <Route path="*" element={<NotFound />} />
                        </Routes>
                      </Container>
                      <Footer />
                    </Box>
                  </>
                )}
              </AuthContext.Consumer>
            </Box>
          </LocalizationProvider>
        </ThemeProvider>
      </EventProvider>
    </AuthProvider>
  );
};

// Main App component that wraps the AppContent with Router
const App = () => {
  return (
    <Router>
      <AppContent />
    </Router>
  );
};

export default App;

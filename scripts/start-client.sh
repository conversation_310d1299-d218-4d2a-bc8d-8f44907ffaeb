#!/bin/bash

# <PERSON><PERSON>t to start the React client with error handling and auto-restart

CLIENT_DIR="client"
MAX_RETRIES=3
RETRY_COUNT=0
RESTART_DELAY=5

echo "Starting React client with error handling..."

start_client() {
    echo "Starting React development server (attempt $((RETRY_COUNT + 1))/$MAX_RETRIES)..."
    cd "$CLIENT_DIR" && npm start
}

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    start_client
    EXIT_CODE=$?
    
    if [ $EXIT_CODE -eq 0 ]; then
        echo "Client exited normally"
        break
    else
        echo "Client crashed with exit code $EXIT_CODE"
        
        if [ $RETRY_COUNT -lt $((MAX_RETRIES - 1)) ]; then
            echo "Restarting client in $RESTART_DELAY seconds..."
            sleep $RESTART_DELAY
            RETRY_COUNT=$((RETRY_COUNT + 1))
        else
            echo "Maximum retry attempts reached. Client will not restart."
            exit $EXIT_CODE
        fi
    fi
done

echo "Client script finished"

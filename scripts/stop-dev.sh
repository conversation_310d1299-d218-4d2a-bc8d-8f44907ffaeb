#!/bin/bash

# Stop all Node.js processes related to the event planner application
echo "Stopping all event planner development processes..."

# Function to gracefully stop a process
graceful_stop() {
    local pattern="$1"
    local name="$2"

    echo "Stopping $name processes..."

    # First try SIGTERM for graceful shutdown
    pkill -TERM -f "$pattern" 2>/dev/null || true

    # Wait a bit for graceful shutdown
    sleep 2

    # Check if processes are still running
    if pgrep -f "$pattern" > /dev/null 2>&1; then
        echo "  $name processes still running, sending SIGINT..."
        pkill -INT -f "$pattern" 2>/dev/null || true
        sleep 2

        # If still running, force kill
        if pgrep -f "$pattern" > /dev/null 2>&1; then
            echo "  Force killing $name processes..."
            pkill -KILL -f "$pattern" 2>/dev/null || true
        fi
    fi

    echo "  $name processes stopped."
}

# Stop processes in order
graceful_stop "concurrently.*npm run server.*npm run client" "concurrently"
graceful_stop "nodemon server/server.js" "nodemon server"
graceful_stop "node server/server.js" "node server"
graceful_stop "react-scripts" "react-scripts"
graceful_stop "node.*client" "client node"

# Final check and cleanup
echo "Performing final cleanup..."
sleep 1

# Kill any remaining processes more aggressively if needed
pkill -9 -f "event-planner" 2>/dev/null || true

echo "All development processes have been stopped."

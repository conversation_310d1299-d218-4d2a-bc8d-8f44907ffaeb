<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Tooltip Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .instructions {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>Mobile Tooltip Test Instructions</h1>
    
    <div class="instructions">
        <h2>How to Test Mobile Tooltip Functionality</h2>
        <ol>
            <li>Open the Event Planner application at <a href="http://localhost:3001" target="_blank">http://localhost:3001</a></li>
            <li>Log in to your account</li>
            <li>Navigate to the Calendar page and select Daily View</li>
            <li>To simulate mobile device:</li>
            <ul>
                <li><strong>Chrome/Edge:</strong> Press F12 → Click device toggle icon (📱) → Select a mobile device</li>
                <li><strong>Firefox:</strong> Press F12 → Click responsive design mode icon → Select a mobile device</li>
                <li><strong>Safari:</strong> Develop menu → Enter Responsive Design Mode</li>
            </ul>
            <li>Ensure the viewport width is less than 960px (md breakpoint)</li>
            <li>Create or find existing tasks in the daily view</li>
            <li>Single click on a task card</li>
            <li>Verify that a dialog opens showing task details (tooltip content)</li>
            <li>Double click on a task card</li>
            <li>Verify that the task edit dialog opens</li>
        </ol>
    </div>

    <div class="test-info">
        <h2>Expected Behavior</h2>
        <ul>
            <li><strong>Desktop (width ≥ 960px):</strong> Hover shows tooltip, single click selects, double click edits</li>
            <li><strong>Mobile (width < 960px):</strong> Single click shows tooltip dialog, double click edits</li>
            <li><strong>Tooltip Dialog Content:</strong> Task name, status, time, location, assignees, description, and edit button</li>
        </ul>
    </div>

    <div class="test-info">
        <h2>Implementation Details</h2>
        <p>The mobile tooltip functionality has been added to the DailyView component:</p>
        <ul>
            <li>Uses Material-UI's <code>useMediaQuery(theme.breakpoints.down('md'))</code> to detect mobile</li>
            <li>Adds click handler to task cards on mobile devices</li>
            <li>Shows tooltip content in a dialog instead of hover tooltip</li>
            <li>Includes edit button in the dialog for easy task editing</li>
            <li>Uses timeout to distinguish single vs double click (200ms delay)</li>
        </ul>
    </div>

    <script>
        // Add some JavaScript to help with testing
        function checkViewport() {
            const width = window.innerWidth;
            const isMobile = width < 960;
            const status = document.getElementById('viewport-status');
            if (status) {
                status.textContent = `Current viewport: ${width}px (${isMobile ? 'Mobile' : 'Desktop'} mode)`;
                status.style.color = isMobile ? 'green' : 'blue';
            }
        }

        // Create viewport status indicator
        const statusDiv = document.createElement('div');
        statusDiv.id = 'viewport-status';
        statusDiv.style.position = 'fixed';
        statusDiv.style.top = '10px';
        statusDiv.style.right = '10px';
        statusDiv.style.background = 'white';
        statusDiv.style.padding = '10px';
        statusDiv.style.border = '1px solid #ccc';
        statusDiv.style.borderRadius = '5px';
        statusDiv.style.fontSize = '14px';
        statusDiv.style.zIndex = '9999';
        document.body.appendChild(statusDiv);

        // Update status on load and resize
        checkViewport();
        window.addEventListener('resize', checkViewport);
    </script>
</body>
</html>

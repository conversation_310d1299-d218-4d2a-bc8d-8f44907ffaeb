{"name": "event-planner", "version": "1.0.0", "description": "Event planning and management application", "main": "server/server.js", "scripts": {"start": "node server/server.js", "server": "nodemon server/server.js --delay 2", "client": "npm start --prefix client", "dev": "concurrently --restart-tries 3 --restart-after 2000 \"npm run server\" \"npm run client\"", "stop-dev": "./scripts/stop-dev.sh", "install-client": "cd client && npm install", "install-server": "npm install", "install-all": "npm run install-server && npm run install-client", "build": "cd client && npm run build", "start-mongo": "mkdir -p ./data/db && mongod --dbpath=./data/db", "dev-with-mongo": "concurrently --kill-others-on-fail \"npm run start-mongo\" \"npm run server\" \"npm run client\"", "dev:seed": "concurrently --kill-others-on-fail \"npm run server:seed\" \"npm run client\"", "server:seed": "cd server && nodemon server.js --seed --delay 2", "seed": "cd server && node scripts/seedDatabase.js", "docker-mongo": "docker run --name event-planner-mongo -p 27017:27017 -d mongo:5.0", "docker-mongo-stop": "docker stop event-planner-mongo && docker rm event-planner-mongo", "wait-for-mongo": "node -e \"const net=require('net');const client=net.createConnection(27017,'localhost');client.on('connect',()=>{console.log('MongoDB is ready');process.exit(0)});client.on('error',()=>{setTimeout(()=>{client.connect()},1000)});\"", "dev-docker": "concurrently --kill-others-on-fail \"npm run docker-mongo\" \"sleep 5 && npm run server\" \"npm run client\"", "dev:with-db": "node scripts/start-with-mongodb.js"}, "keywords": ["event", "planner", "management", "wedding", "task"], "author": "", "license": "MIT", "dependencies": {"@emotion/react": "11.10.6", "@emotion/styled": "11.10.6", "@mui/icons-material": "5.11.16", "@mui/material": "5.12.0", "@mui/x-date-pickers": "6.2.0", "@react-oauth/google": "0.12.1", "axios": "1.3.5", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dagre": "^0.8.5", "date-fns": "2.29.3", "dotenv": "^16.0.3", "express": "^4.18.2", "http-status-codes": "^2.2.0", "i18next": "24.2.3", "i18next-browser-languagedetector": "8.0.4", "i18next-http-backend": "3.0.2", "jsonwebtoken": "^9.0.0", "moment": "2.29.4", "mongoose": "^7.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "react": "18.2.0", "react-big-calendar": "1.6.9", "react-dom": "18.2.0", "react-i18next": "15.4.1", "react-router-dom": "6.10.0", "react-scripts": "5.0.1", "reactflow": "11.7.0"}, "devDependencies": {"concurrently": "^8.0.1", "nodemon": "^2.0.22"}}